<?php
/**
 * Excel Template Converter
 * 
 * Helps users convert their existing Excel templates to the new format
 * with proper field names and date formatting.
 */

session_start();

echo "<h2>🔄 Excel Template Converter</h2>";
echo "<p>This tool helps convert your existing Excel templates to work with the enhanced import system.</p>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    $uploadedFile = $_FILES['excel_file'];
    
    if ($uploadedFile['error'] === UPLOAD_ERR_OK) {
        $tempFile = $uploadedFile['tmp_name'];
        $originalName = $uploadedFile['name'];
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        
        if ($extension === 'xlsx') {
            echo "<h3>📊 Processing: " . htmlspecialchars($originalName) . "</h3>";
            
            // Include the conversion function
            function excelSerialDateToDate($serialDate) {
                if (!is_numeric($serialDate) || $serialDate <= 0) {
                    return null;
                }
                
                $excelEpoch = new DateTime('1899-12-30');
                $days = floor($serialDate);
                
                try {
                    $date = clone $excelEpoch;
                    $date->add(new DateInterval('P' . $days . 'D'));
                    return $date->format('Y-m-d');
                } catch (Exception $e) {
                    return null;
                }
            }
            
            // Simple Excel reading
            $zip = new ZipArchive();
            if ($zip->open($tempFile) === TRUE) {
                $sharedStrings = [];
                
                // Read shared strings
                $sharedStringsXml = $zip->getFromName('xl/sharedStrings.xml');
                if ($sharedStringsXml) {
                    $xml = simplexml_load_string($sharedStringsXml);
                    foreach ($xml->si as $si) {
                        $sharedStrings[] = (string)$si->t;
                    }
                }
                
                // Read first worksheet
                $worksheetXml = $zip->getFromName('xl/worksheets/sheet1.xml');
                if ($worksheetXml) {
                    $xml = simplexml_load_string($worksheetXml);
                    $rows = [];
                    $headerRow = null;
                    
                    foreach ($xml->sheetData->row as $row) {
                        $rowData = [];
                        $rowIndex = (int)$row['r'] - 1;
                        
                        foreach ($row->c as $cell) {
                            $value = '';
                            if (isset($cell->v)) {
                                if (isset($cell['t']) && $cell['t'] == 's') {
                                    $index = (int)$cell->v;
                                    $value = isset($sharedStrings[$index]) ? $sharedStrings[$index] : '';
                                } else {
                                    $rawValue = (string)$cell->v;
                                    // Try to convert dates
                                    if (is_numeric($rawValue) && $rawValue > 40000 && $rawValue < 50000) {
                                        $dateValue = excelSerialDateToDate($rawValue);
                                        $value = $dateValue ?: $rawValue;
                                    } else {
                                        $value = $rawValue;
                                    }
                                }
                            }
                            $rowData[] = $value;
                        }
                        
                        if ($rowIndex === 0) {
                            $headerRow = $rowData;
                        }
                        $rows[] = $rowData;
                    }
                    
                    $zip->close();
                    
                    // Show original headers
                    echo "<h4>📋 Original Headers</h4>";
                    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
                    echo "<p>" . implode(', ', array_map('htmlspecialchars', $headerRow)) . "</p>";
                    echo "</div>";
                    
                    // Apply field mapping
                    $fieldMapping = [
                        'nhif_number' => 'shif_id',
                        'account_number' => 'bank_account',
                        'department' => 'department_name',
                        'position' => 'position_title'
                    ];
                    
                    $mappedHeaders = array_map(function($header) use ($fieldMapping) {
                        $header = strtolower(trim($header));
                        return $fieldMapping[$header] ?? $header;
                    }, $headerRow);
                    
                    echo "<h4>🔄 Mapped Headers</h4>";
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
                    echo "<p>" . implode(', ', array_map('htmlspecialchars', $mappedHeaders)) . "</p>";
                    echo "</div>";
                    
                    // Generate CSV
                    $csvContent = implode(',', array_map(function($field) {
                        return '"' . str_replace('"', '""', $field) . '"';
                    }, $mappedHeaders)) . "\n";
                    
                    // Add data rows (skip header)
                    for ($i = 1; $i < min(count($rows), 6); $i++) {
                        $row = $rows[$i];
                        // Ensure row has same number of columns as header
                        while (count($row) < count($mappedHeaders)) {
                            $row[] = '';
                        }
                        $csvContent .= implode(',', array_map(function($field) {
                            return '"' . str_replace('"', '""', trim($field)) . '"';
                        }, array_slice($row, 0, count($mappedHeaders)))) . "\n";
                    }
                    
                    echo "<h4>📄 Converted CSV Preview (First 5 rows)</h4>";
                    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
                    echo htmlspecialchars($csvContent);
                    echo "</pre>";
                    
                    // Offer download
                    $csvFileName = pathinfo($originalName, PATHINFO_FILENAME) . '_converted.csv';
                    echo "<div style='text-align: center; margin: 20px 0;'>";
                    echo "<form method='post' style='display: inline;'>";
                    echo "<input type='hidden' name='csv_content' value='" . htmlspecialchars($csvContent) . "'>";
                    echo "<input type='hidden' name='csv_filename' value='" . htmlspecialchars($csvFileName) . "'>";
                    echo "<button type='submit' name='download_csv' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>📥 Download Converted CSV</button>";
                    echo "</form>";
                    echo "</div>";
                    
                } else {
                    echo "<p style='color: red;'>❌ Could not read worksheet data</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Could not open Excel file</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Please upload an Excel (.xlsx) file</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ File upload error</p>";
    }
}

// Handle CSV download
if (isset($_POST['download_csv']) && isset($_POST['csv_content']) && isset($_POST['csv_filename'])) {
    $csvContent = $_POST['csv_content'];
    $csvFileName = $_POST['csv_filename'];
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $csvFileName . '"');
    header('Content-Length: ' . strlen($csvContent));
    
    echo $csvContent;
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Excel Template Converter</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        
        <?php if (!isset($_POST['excel_file'])): ?>
        
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-excel"></i> Upload Excel Template</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="excel_file" class="form-label">Select Excel File (.xlsx)</label>
                        <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx" required>
                        <div class="form-text">Upload your existing Excel template to convert it to the new format</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Convert Excel to CSV
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> What This Tool Does</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🔄 Field Name Mapping:</h6>
                        <ul class="small">
                            <li><code>nhif_number</code> → <code>shif_id</code></li>
                            <li><code>account_number</code> → <code>bank_account</code></li>
                            <li><code>department</code> → <code>department_name</code></li>
                            <li><code>position</code> → <code>position_title</code></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>📅 Date Conversion:</h6>
                        <ul class="small">
                            <li>Converts Excel serial dates to Y-m-d format</li>
                            <li>Example: 45321 → 2024-01-15</li>
                            <li>Preserves text dates as-is</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <?php endif; ?>
        
        <div class="text-center mt-4">
            <a href="index.php?page=employees&action=bulk_import" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Bulk Import
            </a>
            <a href="test_excel_import_fix.php" class="btn btn-info ms-2">
                <i class="fas fa-vial"></i> Test Import Fixes
            </a>
        </div>
        
    </div>
</body>
</html>
