<!DOCTYPE html>
<html>
<head>
    <title>Generate Kenyan Flag Favicon</title>
</head>
<body>
    <h2>Kenyan Flag Favicon Generator</h2>
    <canvas id="canvas" width="32" height="32" style="border: 1px solid #ccc; image-rendering: pixelated; width: 128px; height: 128px;"></canvas>
    <br><br>
    <button onclick="generateFavicon()">Generate Favicon</button>
    <button onclick="downloadFavicon()">Download as ICO</button>
    
    <script>
        function generateFavicon() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, 32, 32);

            // Authentic Kenyan flag stripes with proper proportions
            const stripes = [
                { color: '#000000', start: 0, height: 5.33 },      // Black
                { color: '#FFFFFF', start: 5.33, height: 2.67 },   // White
                { color: '#CE1126', start: 8, height: 5.33 },      // Red
                { color: '#FFFFFF', start: 13.33, height: 2.67 },  // White
                { color: '#007A3D', start: 16, height: 5.33 },     // Green
                { color: '#FFFFFF', start: 21.33, height: 2.67 },  // White
                { color: '#CE1126', start: 24, height: 5.33 },     // Red
                { color: '#FFFFFF', start: 29.33, height: 2.67 }   // White
            ];

            // Draw flag stripes
            stripes.forEach(stripe => {
                ctx.fillStyle = stripe.color;
                ctx.fillRect(0, stripe.start, 32, stripe.height);
            });

            // Enhanced shield in center
            const centerX = 16;
            const centerY = 16;

            // Shield background (white)
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, 7, 9, 0, 0, 2 * Math.PI);
            ctx.fill();

            // Shield border
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 1;
            ctx.stroke();

            // Shield pattern layers
            const shieldLayers = [
                { color: '#8B4513', rx: 6, ry: 8 },
                { color: '#D2691E', rx: 5, ry: 7 },
                { color: '#FFFFFF', rx: 4, ry: 6 },
                { color: '#CE1126', rx: 3, ry: 5 },
                { color: '#000000', rx: 2, ry: 4 }
            ];

            shieldLayers.forEach(layer => {
                ctx.fillStyle = layer.color;
                ctx.beginPath();
                ctx.ellipse(centerX, centerY, layer.rx, layer.ry, 0, 0, 2 * Math.PI);
                ctx.fill();
            });

            // Crossed spears
            ctx.strokeStyle = '#654321';
            ctx.lineWidth = 2;
            ctx.beginPath();

            // Left spear (diagonal)
            ctx.moveTo(6, 6);
            ctx.lineTo(26, 26);

            // Right spear (diagonal)
            ctx.moveTo(26, 6);
            ctx.lineTo(6, 26);

            ctx.stroke();

            // Spear tips
            ctx.fillStyle = '#C0C0C0';
            const tipSize = 2;

            // Top-left tip
            ctx.fillRect(5, 5, tipSize, tipSize);
            // Top-right tip
            ctx.fillRect(25, 5, tipSize, tipSize);
            // Bottom-left tip
            ctx.fillRect(5, 25, tipSize, tipSize);
            // Bottom-right tip
            ctx.fillRect(25, 25, tipSize, tipSize);
        }
        
        function downloadFavicon() {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = 'favicon.ico';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Generate on load
        generateFavicon();
    </script>
</body>
</html>
