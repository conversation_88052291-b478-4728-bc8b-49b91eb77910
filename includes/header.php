<?php
/**
 * Header navigation
 */
?>
<nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: linear-gradient(135deg, #007A3D, #CE1126, #000000); overflow: visible; z-index: 1030;">
    <!-- Kenyan flag pattern background with subtle animation -->
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0.1; background: repeating-linear-gradient(90deg, #000000 0%, #CE1126 20%, #007A3D 40%, #FFFFFF 60%, #CE1126 80%, #000000 100%); animation: flagShimmer 8s ease-in-out infinite;"></div>

    <div class="container-fluid" style="position: relative; z-index: 2; overflow: visible;">
        <a class="navbar-brand" href="index.php" style="position: relative; z-index: 3;">
            <i class="fas fa-calculator" style="color: #FFFFFF;"></i>
            <span style="font-weight: bold;">Kenyan Payroll</span>
            <small style="display: block; font-size: 11px; opacity: 0.9; color: #FFFFFF;">🇰🇪 Management System</small>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" style="position: relative; z-index: 3; border-color: rgba(255,255,255,0.5);">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav" style="position: relative; z-index: 2;">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php?page=dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#payrollCalculatorModal">
                        <i class="fas fa-calculator text-success"></i> 🇰🇪 Calculator
                    </a>
                </li>
                
                <?php if (hasPermission('hr')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="employeesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i> Employees
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.php?page=employees">View All</a></li>
                            <li><a class="dropdown-item" href="index.php?page=employees&action=add">Add Employee</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.php?page=departments">Departments</a></li>
                            <li><a class="dropdown-item" href="index.php?page=positions">Job Positions</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="payrollDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calculator"></i> Payroll
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#payrollCalculatorModal">
                                <i class="fas fa-calculator text-success"></i> 🇰🇪 Payroll Calculator
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.php?page=simple_payroll">
                                <i class="fas fa-bolt text-primary"></i> Quick Payroll
                            </a></li>
                            <li><a class="dropdown-item" href="index.php?page=payroll">Payroll Periods</a></li>
                            <li><a class="dropdown-item" href="index.php?page=payroll&action=create">Process Payroll</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.php?page=payroll_management">
                                <i class="fas fa-cogs text-warning"></i> Payroll Management
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.php?page=allowances">Allowances</a></li>
                            <li><a class="dropdown-item" href="index.php?page=deductions">Deductions</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="leavesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar-alt"></i> Leaves
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.php?page=leaves">Leave Applications</a></li>
                            <li><a class="dropdown-item" href="index.php?page=leave_types">Leave Types</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-alt"></i> Reports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.php?page=reports&type=payroll">Payroll Reports</a></li>
                            <li><a class="dropdown-item" href="index.php?page=reports&type=statutory">Statutory Reports</a></li>
                            <li><a class="dropdown-item" href="index.php?page=reports&type=employee">Employee Reports</a></li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=payslips">
                            <i class="fas fa-receipt"></i> My Payslips
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=leaves">
                            <i class="fas fa-calendar-alt"></i> My Leaves
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=profile">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle"></i> <?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="index.php?page=profile">
                            <i class="fas fa-user"></i> Profile
                        </a></li>
                        <li><a class="dropdown-item" href="index.php?page=settings">
                            <i class="fas fa-cog"></i> Settings
                        </a></li>
                        <?php if (hasPermission('admin')): ?>
                            <li><a class="dropdown-item" href="index.php?page=security_dashboard">
                                <i class="fas fa-shield-alt"></i> Security Dashboard
                            </a></li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="secure_auth.php?action=logout">
                            <i class="fas fa-sign-out-alt"></i> Secure Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Enhanced Kenyan Flag Header Styling -->
<style>
    /* Ensure proper fixed header behavior */
    .navbar.fixed-top {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1030 !important;
        overflow: visible !important;
    }

    /* Ensure dropdown container has proper z-index and overflow */
    .navbar .dropdown {
        position: relative !important;
        z-index: 10000 !important;
        overflow: visible !important;
    }

    /* Ensure navbar collapse doesn't clip dropdown */
    .navbar-collapse {
        overflow: visible !important;
    }

    /* Remove any margin/padding that might cause white space */
    html, body {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure main content starts below fixed header */
    .main-content {
        padding-top: 80px !important; /* Account for navbar height + spacing */
        min-height: calc(100vh - 56px) !important; /* Adjust for navbar height */
        position: relative !important;
        z-index: 1 !important; /* Lower than dropdown z-index */
    }

    /* Ensure containers have proper spacing */
    .main-content .container-fluid:first-child,
    .main-content .container:first-child {
        padding-top: 20px !important;
    }

    /* Enhance navbar links visibility with Kenyan flag background */
    .navbar-nav .nav-link {
        color: #FFFFFF !important;
        font-weight: 500;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        transition: all 0.3s ease;
        position: relative;
    }

    .navbar-nav .nav-link:hover {
        color: #FFFFFF !important;
        background: rgba(255,255,255,0.1);
        border-radius: 6px;
        transform: translateY(-1px);
        text-shadow: 1px 1px 3px rgba(0,0,0,0.7);
    }

    .navbar-nav .nav-link.active {
        background: rgba(255,255,255,0.2);
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }

    /* Dropdown menu enhancements */
    .dropdown-menu {
        background: #ffffff !important;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(0,122,61,0.2) !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        z-index: 9999 !important;
        min-width: 200px !important;
        position: absolute !important;
    }

    .navbar .dropdown-item {
        color: #333333;
        padding: 10px 20px;
        transition: all 0.3s ease;
        background: #ffffff;
    }

    .navbar .dropdown-item:hover,
    .navbar .dropdown-item:focus {
        background: linear-gradient(135deg, rgba(0,122,61,0.1), rgba(206,17,38,0.1));
        color: #007A3D;
    }

    .navbar .dropdown-item i {
        color: inherit;
        margin-right: 8px;
    }

    /* Dropdown divider styling */
    .dropdown-divider {
        border-top: 1px solid rgba(0,122,61,0.2) !important;
        margin: 8px 0 !important;
    }

    /* Ensure dropdown shows properly - simplified */
    .dropdown-menu.show {
        display: block !important;
        z-index: 9999 !important;
        position: absolute !important;
    }

    .dropdown-menu {
        display: none;
        z-index: 9999 !important;
        position: absolute !important;
    }

    /* Ensure all content stays below dropdown */
    .main-content {
        z-index: 1 !important;
        position: relative !important;
    }

    .main-content * {
        z-index: auto !important;
    }

    /* Specifically target common elements that might interfere */
    .container,
    .container-fluid,
    .card,
    .row,
    .col,
    .alert,
    .table,
    .btn-group,
    .modal-content {
        z-index: auto !important;
    }

    /* Clean dropdown menu styling */
    .navbar .dropdown-menu {
        z-index: 1050;
        position: absolute;
        background: #ffffff;
        border: 1px solid rgba(0,122,61,0.2);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        min-width: 200px;
    }

    .navbar .dropdown-menu.show {
        display: block;
    }

    /* Navbar brand enhancement */
    .navbar-brand {
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        transition: all 0.3s ease;
    }

    .navbar-brand:hover {
        transform: scale(1.05);
        text-shadow: 2px 2px 6px rgba(0,0,0,0.7);
    }

    /* User dropdown styling - simplified */
    #userDropdown {
        background: rgba(255,255,255,0.1);
        border-radius: 20px;
        padding: 8px 15px;
        border: 1px solid rgba(255,255,255,0.2);
        color: #FFFFFF;
        text-decoration: none;
        cursor: pointer;
    }

    #userDropdown:hover,
    #userDropdown:focus {
        background: rgba(255,255,255,0.2);
        color: #FFFFFF;
        text-decoration: none;
    }

    /* Ensure dropdown toggle is clickable */
    .dropdown-toggle {
        color: #FFFFFF;
        cursor: pointer;
        pointer-events: auto;
    }

    .dropdown-toggle:hover,
    .dropdown-toggle:focus {
        color: #FFFFFF;
    }

    /* Fix dropdown arrow visibility */
    .dropdown-toggle::after {
        border-top-color: #FFFFFF;
    }

    /* Kenyan flag shimmer animation */
    @keyframes flagShimmer {
        0%, 100% {
            opacity: 0.1;
            transform: translateX(0px);
        }
        50% {
            opacity: 0.15;
            transform: translateX(2px);
        }
    }

    /* Mobile responsiveness */
    @media (max-width: 991px) {
        .navbar-collapse {
            background: rgba(0,0,0,0.9);
            margin-top: 10px;
            border-radius: 10px;
            padding: 15px;
        }

        .main-content {
            padding-top: 70px !important; /* Slightly less padding on mobile */
        }
    }

    @media (max-width: 576px) {
        .main-content {
            padding-top: 65px !important; /* Even less padding on small screens */
        }
    }

    /* Final dropdown styling with proper Kenyan theme */
    .navbar .dropdown-menu {
        background: #ffffff !important;
        border: 1px solid rgba(0,122,61,0.2) !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        min-width: 200px !important;
    }

    .navbar .dropdown-item {
        background: #ffffff !important;
        color: #333333 !important;
    }

    .navbar .dropdown-item:hover {
        background: linear-gradient(135deg, rgba(0,122,61,0.1), rgba(206,17,38,0.1)) !important;
        color: #007A3D !important;
    }
</style>

<!-- Let Bootstrap handle dropdowns completely -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Just verify Bootstrap is available
    if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
        console.log('✅ Bootstrap dropdowns available');
    } else {
        console.error('❌ Bootstrap dropdowns not available');
    }
});
</script>

<style>
body {
    padding-top: 70px;
}

.main-content {
    margin-left: 0;
    padding: 20px;
}

@media (min-width: 768px) {
    .main-content {
        margin-left: 250px;
    }
}
</style>
