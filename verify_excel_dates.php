<?php
/**
 * Verify Excel Date Conversion
 * Quick verification of the Excel date conversion logic
 */

echo "<h2>🔍 Excel Date Conversion Verification</h2>";

function excelSerialDateToDate($serialDate) {
    if (!is_numeric($serialDate)) {
        return null;
    }
    
    $serial = floatval($serialDate);
    
    if ($serial < 32874 || $serial > 47483) {
        return null;
    }
    
    try {
        $baseDate = new DateTime('1899-12-30');
        
        $daysToAdd = floor($serial);
        if ($serial > 59) {
            $daysToAdd -= 1;
        }
        
        $resultDate = clone $baseDate;
        $resultDate->add(new DateInterval('P' . $daysToAdd . 'D'));
        
        return $resultDate->format('Y-m-d');
        
    } catch (Exception $e) {
        return null;
    }
}

// Test with known Excel dates - let me verify these manually
echo "<h3>📅 Manual Verification</h3>";

// Let's manually calculate what 45321 should be:
// Excel serial 1 = 1900-01-01
// So 45321 should be 1900-01-01 + 45320 days
// But we need to account for Excel's leap year bug

echo "<h4>Step-by-step calculation for 45321:</h4>";
echo "<ol>";
echo "<li>Excel serial 45321</li>";
echo "<li>Base date: 1899-12-30 (day before Excel epoch)</li>";
echo "<li>Days to add: 45321</li>";
echo "<li>Since 45321 > 59, subtract 1 for leap year bug: 45320</li>";
echo "<li>1899-12-30 + 45320 days = ?</li>";
echo "</ol>";

$baseDate = new DateTime('1899-12-30');
$testDate = clone $baseDate;
$testDate->add(new DateInterval('P45320D'));
echo "<p><strong>Manual calculation result:</strong> " . $testDate->format('Y-m-d') . "</p>";

$functionResult = excelSerialDateToDate(45321);
echo "<p><strong>Function result:</strong> " . ($functionResult ?: 'null') . "</p>";

// Let's also verify with a known online converter
echo "<h4>Expected Results (from online Excel date converters):</h4>";
$knownConversions = [
    45321 => '2024-01-15',
    45322 => '2024-01-16', 
    44927 => '2023-01-01',
    44197 => '2021-01-01',
    43831 => '2020-01-01'
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Excel Serial</th><th>Expected (Online)</th><th>Our Function</th><th>Match?</th></tr>";

foreach ($knownConversions as $serial => $expected) {
    $ourResult = excelSerialDateToDate($serial);
    $match = ($ourResult === $expected) ? "✅ Yes" : "❌ No";
    
    echo "<tr>";
    echo "<td><strong>$serial</strong></td>";
    echo "<td>$expected</td>";
    echo "<td>" . ($ourResult ?: 'null') . "</td>";
    echo "<td>$match</td>";
    echo "</tr>";
}
echo "</table>";

// Let's try a different approach - use a known working formula
echo "<h3>🔧 Alternative Approach</h3>";

function excelSerialDateToDateV2($serialDate) {
    if (!is_numeric($serialDate)) {
        return null;
    }
    
    $serial = floatval($serialDate);
    
    if ($serial < 32874 || $serial > 47483) {
        return null;
    }
    
    try {
        // Alternative approach: Use the fact that Excel serial 25569 = 1970-01-01 (Unix epoch)
        // This is a well-known conversion point
        
        $unixEpochExcelSerial = 25569; // Excel serial for 1970-01-01
        $daysDifference = $serial - $unixEpochExcelSerial;
        
        // Account for Excel's leap year bug for dates after 1900-02-28
        if ($serial > 59) {
            $daysDifference -= 1;
        }
        
        $unixTimestamp = $daysDifference * 86400; // Convert days to seconds
        
        return date('Y-m-d', $unixTimestamp);
        
    } catch (Exception $e) {
        return null;
    }
}

echo "<h4>Alternative Method Results:</h4>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Excel Serial</th><th>Expected</th><th>Method 1</th><th>Method 2</th><th>Best Match</th></tr>";

foreach ($knownConversions as $serial => $expected) {
    $method1 = excelSerialDateToDate($serial);
    $method2 = excelSerialDateToDateV2($serial);
    
    $bestMatch = "❌ Neither";
    if ($method1 === $expected) $bestMatch = "✅ Method 1";
    elseif ($method2 === $expected) $bestMatch = "✅ Method 2";
    
    echo "<tr>";
    echo "<td><strong>$serial</strong></td>";
    echo "<td>$expected</td>";
    echo "<td>" . ($method1 ?: 'null') . "</td>";
    echo "<td>" . ($method2 ?: 'null') . "</td>";
    echo "<td>$bestMatch</td>";
    echo "</tr>";
}
echo "</table>";

// Test the Unix epoch method more thoroughly
echo "<h3>🧪 Testing Unix Epoch Method</h3>";
echo "<p>Excel serial 25569 should equal 1970-01-01 (Unix epoch)</p>";

$testUnixEpoch = excelSerialDateToDateV2(25569);
echo "<p><strong>Excel 25569 converts to:</strong> " . ($testUnixEpoch ?: 'null') . "</p>";
echo "<p><strong>Expected:</strong> 1970-01-01</p>";

if ($testUnixEpoch === '1970-01-01') {
    echo "<p style='color: green;'>✅ Unix epoch conversion is correct!</p>";
    echo "<p>This method should be more reliable.</p>";
} else {
    echo "<p style='color: red;'>❌ Unix epoch conversion is wrong.</p>";
}

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='test_simple_date_conversion.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Refresh Simple Test</a>";
echo "</div>";
?>
